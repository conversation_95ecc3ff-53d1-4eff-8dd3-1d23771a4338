import { useAuth } from "@/hooks/useAuth";
import { createTestProfile } from "@/utils/testDatabase";

export default function AuthDebug() {
  const { user, profile, loading, session } = useAuth();

  const handleCreateProfile = async () => {
    if (user?.id && user?.email) {
      await createTestProfile(user.id, user.email);
      window.location.reload();
    }
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">Auth Debug Info</h3>
      <div className="space-y-1">
        <div>Loading: {loading ? 'true' : 'false'}</div>
        <div>User ID: {user?.id || 'null'}</div>
        <div>User Email: {user?.email || 'null'}</div>
        <div>Profile ID: {profile?.id || 'null'}</div>
        <div>Profile Role: {profile?.role || 'null'}</div>
        <div>Profile Username: {profile?.username || 'null'}</div>
        <div>Session: {session ? 'exists' : 'null'}</div>
        <div>Has Admin Access: {(profile?.role === 'admin' || profile?.role === 'editor') ? 'true' : 'false'}</div>
      </div>
      {user && !profile && (
        <button
          onClick={handleCreateProfile}
          className="mt-2 bg-red-600 text-white px-2 py-1 rounded text-xs"
        >
          Create Profile
        </button>
      )}
    </div>
  );
}
