# Database Setup Guide

## Issue Fixed
The main issue was that when users registered, their authentication record was created in `auth.users` but no corresponding profile was created in the `user_profiles` table. This caused profiles to not show up in Supabase.

## Solution
1. **Database Trigger**: Added an automatic trigger that creates a user profile whenever a new user signs up
2. **Improved Auth Hook**: Enhanced the authentication logic to handle profile creation and error scenarios
3. **Row Level Security**: Added proper RLS policies for data security

## Steps to Fix Your Database

### 1. Run the Database Updates
Go to your Supabase project dashboard:
1. Navigate to **SQL Editor**
2. Copy and paste the contents of `database-updates.sql`
3. Click **Run** to execute the SQL

### 2. Verify the Setup
After running the SQL, you can verify the setup:

```sql
-- Check if the trigger function exists
SELECT proname FROM pg_proc WHERE proname = 'handle_new_user';

-- Check if the trigger exists
SELECT tgname FROM pg_trigger WHERE tgname = 'on_auth_user_created';

-- Check RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('user_profiles', 'articles', 'comments', 'bookmarks');
```

### 3. Test Registration
1. Try registering a new user through your application
2. Check the `user_profiles` table in Supabase to see if the profile was created automatically
3. The profile should appear immediately after registration

## What the Fix Does

### Database Trigger Function
```sql
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, username, full_name, avatar_url, role)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || NEW.id::text),
        'user'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

This function:
- Automatically runs when a new user is created in `auth.users`
- Extracts user data from the registration metadata
- Creates a corresponding profile in `user_profiles`
- Sets default values if metadata is missing

### Frontend Improvements
The `useAuth` hook now:
- Checks for missing profiles and attempts to create them
- Handles username uniqueness validation
- Provides better error handling and user feedback
- Waits for profile creation after registration

## Row Level Security Policies
Added comprehensive RLS policies to ensure:
- Users can only access their own data
- Public data (like articles) is accessible to everyone
- Admin-only features are properly protected

## Troubleshooting

### If profiles still don't appear:
1. Check if the trigger was created successfully
2. Verify RLS policies are not blocking access
3. Check browser console for any errors
4. Try registering with a new email address

### Manual Profile Creation
If you need to create profiles for existing users:
```sql
INSERT INTO user_profiles (id, username, full_name, avatar_url, role)
SELECT 
    id,
    COALESCE(raw_user_meta_data->>'username', split_part(email, '@', 1)),
    COALESCE(raw_user_meta_data->>'full_name', split_part(email, '@', 1)),
    'https://api.dicebear.com/7.x/avataaars/svg?seed=' || id::text,
    'user'
FROM auth.users
WHERE id NOT IN (SELECT id FROM user_profiles);
```

## Environment Variables
Make sure your `.env` file has the correct Supabase credentials:
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Next Steps
After applying these fixes:
1. Test the registration flow thoroughly
2. Verify that profiles appear in Supabase dashboard
3. Test login/logout functionality
4. Check that user data persists correctly
